from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from .constants import MAX_FILE_SIZE_BYTES, ALLOWED_EXTENSIONS, MULTIPART_CHUNK_SIZE_BYTES


class PresignedUrlRequest(BaseModel):
    filename: str = Field(..., description="Name of the file to upload")
    filesize: int = Field(..., description="Size of the file in bytes")

    @field_validator("filename")
    @classmethod
    def validate_filename(cls, v):
        if not v.lower().endswith(ALLOWED_EXTENSIONS):
            raise ValueError("Only .edf files are allowed")
        return v

    @field_validator("filesize")
    @classmethod
    def validate_filesize(cls, v):
        if v > MAX_FILE_SIZE_BYTES:
            raise ValueError("File too large. Maximum size is 1GB")
        if v <= 0:
            raise ValueError("File size must be greater than 0")
        return v


class PresignedUrlResponse(BaseModel):
    url: str = Field(..., description="Presigned URL for file upload")
    key: str = Field(..., description="S3 object key")
    expires_in: int = Field(..., description="URL expiry time in seconds")


class FileInfo(BaseModel):
    key: str = Field(..., description="S3 object key")
    filename: str = Field(..., description="Original filename")
    size: int = Field(..., description="File size in bytes")
    last_modified: datetime = Field(...,
                                    description="Last modification timestamp")
    etag: Optional[str] = Field(None, description="ETag of the object")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class FileListResponse(BaseModel):
    files: List[FileInfo] = Field(...,
                                  description="List of files in the bucket")
    total_count: int = Field(..., description="Total number of files")
    total_size: int = Field(...,
                            description="Total size of all files in bytes")


class FileDeleteRequest(BaseModel):
    key: str = Field(..., description="S3 object key to delete")


class FileDeleteResponse(BaseModel):
    success: bool = Field(..., description="Whether deletion was successful")
    message: str = Field(..., description="Status message")
    deleted_key: str = Field(..., description="Key of the deleted object")


class ErrorResponse(BaseModel):
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Additional error details")


# Multipart Upload Models
class MultipartUploadInitiateRequest(BaseModel):
    filename: str = Field(..., description="Name of the file to upload")
    filesize: int = Field(..., description="Size of the file in bytes")
    chunk_size: int = Field(
        default=MULTIPART_CHUNK_SIZE_BYTES,
        description="Size of each chunk in bytes"
    )

    @field_validator("filename")
    @classmethod
    def validate_filename(cls, v):
        if not v.lower().endswith(ALLOWED_EXTENSIONS):
            raise ValueError("Only .edf files are allowed")
        return v

    @field_validator("filesize")
    @classmethod
    def validate_filesize(cls, v):
        if v > MAX_FILE_SIZE_BYTES:
            raise ValueError("File too large. Maximum size is 1GB")
        if v <= 0:
            raise ValueError("File size must be greater than 0")
        return v


class MultipartUploadInitiateResponse(BaseModel):
    upload_id: str = Field(..., description="Multipart upload ID")
    key: str = Field(..., description="S3 object key")
    chunk_size: int = Field(..., description="Recommended chunk size in bytes")
    total_parts: int = Field(..., description="Total number of parts needed")
    expires_in: int = Field(...,
                            description="Upload session expiry time in seconds")


class MultipartUploadPartRequest(BaseModel):
    key: str = Field(..., description="S3 object key")
    upload_id: str = Field(..., description="Multipart upload ID")
    part_number: int = Field(..., description="Part number (1-based)")

    @field_validator("part_number")
    @classmethod
    def validate_part_number(cls, v):
        if v < 1 or v > 10000:
            raise ValueError("Part number must be between 1 and 10000")
        return v


class MultipartUploadPartResponse(BaseModel):
    upload_url: str = Field(...,
                            description="Presigned URL for uploading this part")
    part_number: int = Field(..., description="Part number")
    expires_in: int = Field(..., description="URL expiry time in seconds")


class MultipartUploadPart(BaseModel):
    part_number: int = Field(..., description="Part number")
    etag: str = Field(..., description="ETag returned from part upload")


class MultipartUploadCompleteRequest(BaseModel):
    key: str = Field(..., description="S3 object key")
    upload_id: str = Field(..., description="Multipart upload ID")
    parts: List[MultipartUploadPart] = Field(...,
                                             description="List of uploaded parts")

    @field_validator("parts")
    @classmethod
    def validate_parts(cls, v):
        if not v:
            raise ValueError("At least one part is required")
        part_numbers = [part.part_number for part in v]
        if len(set(part_numbers)) != len(part_numbers):
            raise ValueError("Duplicate part numbers found")
        return v


class MultipartUploadCompleteResponse(BaseModel):
    success: bool = Field(...,
                          description="Whether upload was completed successfully")
    key: str = Field(..., description="S3 object key")
    etag: str = Field(..., description="ETag of the completed object")
    location: str = Field(..., description="S3 object location")


class MultipartUploadAbortRequest(BaseModel):
    key: str = Field(..., description="S3 object key")
    upload_id: str = Field(..., description="Multipart upload ID")


class MultipartUploadAbortResponse(BaseModel):
    success: bool = Field(...,
                          description="Whether upload was aborted successfully")
    message: str = Field(..., description="Status message")

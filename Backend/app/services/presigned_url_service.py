from typing import Dict, Any, List
from botocore.exceptions import Client<PERSON>rror

from ..config import settings
from ..logging_config import get_logger
from ..constants import (
    DEFAULT_EXPIRY_SECONDS,
    CONTENT_TYPE_OCTET_STREAM,
    METADATA_ORIGINAL_FILENAME,
    S3_CLIENT_METHOD_PUT,
    S3_CLIENT_METHOD_GET,
    S3_CLIENT_METHOD_CREATE_MULTIPART,
    S3_CLIENT_METHOD_UPLOAD_PART,
    S3_CLIENT_METHOD_COMPLETE_MULTIPART,
    S3_CLIENT_METHOD_ABORT_MULTIPART,
)

logger = get_logger(__name__)


class PresignedUrlService:
    def __init__(self, s3_client, bucket_name: str):
        self.s3_client = s3_client
        self.bucket_name = bucket_name

    def _generate_presigned_url(
        self,
        client_method: str,
        params: Dict[str, Any],
        expiry: int = DEFAULT_EXPIRY_SECONDS,
    ) -> str:
        try:
            return self.s3_client.generate_presigned_url(
                ClientMethod=client_method,
                Params=params,
                ExpiresIn=expiry,
            )
        except ClientError as e:
            logger.error(
                f"Error generating presigned URL for {client_method}: {e}"
            )
            raise

    def generate_upload_url(self, key: str, filename: str) -> str:
        params = {
            "Bucket": self.bucket_name,
            "Key": key,
            "ContentType": CONTENT_TYPE_OCTET_STREAM,
            "Metadata": {METADATA_ORIGINAL_FILENAME: filename},
        }
        return self._generate_presigned_url(
            S3_CLIENT_METHOD_PUT,
            params,
            settings.presigned_url_expiry_seconds,
        )

    def generate_download_url(
        self, key: str, expiry: int = DEFAULT_EXPIRY_SECONDS
    ) -> str:
        params = {"Bucket": self.bucket_name, "Key": key}
        return self._generate_presigned_url(S3_CLIENT_METHOD_GET, params, expiry)

    def initiate_multipart_upload(self, key: str, filename: str) -> str:
        """Initiate a multipart upload and return upload ID"""
        try:
            response = self.s3_client.create_multipart_upload(
                Bucket=self.bucket_name,
                Key=key,
                ContentType=CONTENT_TYPE_OCTET_STREAM,
                Metadata={METADATA_ORIGINAL_FILENAME: filename},
            )
            return response["UploadId"]
        except ClientError as e:
            logger.error(f"Error initiating multipart upload for {key}: {e}")
            raise

    def generate_part_upload_url(
        self, key: str, upload_id: str, part_number: int, expiry: int = DEFAULT_EXPIRY_SECONDS
    ) -> str:
        """Generate presigned URL for uploading a specific part"""
        params = {
            "Bucket": self.bucket_name,
            "Key": key,
            "UploadId": upload_id,
            "PartNumber": part_number,
        }
        return self._generate_presigned_url(S3_CLIENT_METHOD_UPLOAD_PART, params, expiry)

    def complete_multipart_upload(
        self, key: str, upload_id: str, parts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Complete multipart upload with list of parts"""
        try:
            response = self.s3_client.complete_multipart_upload(
                Bucket=self.bucket_name,
                Key=key,
                UploadId=upload_id,
                MultipartUpload={"Parts": parts},
            )
            return response
        except ClientError as e:
            logger.error(f"Error completing multipart upload for {key}: {e}")
            raise

    def abort_multipart_upload(self, key: str, upload_id: str) -> None:
        """Abort multipart upload"""
        try:
            self.s3_client.abort_multipart_upload(
                Bucket=self.bucket_name, Key=key, UploadId=upload_id
            )
        except ClientError as e:
            logger.error(f"Error aborting multipart upload for {key}: {e}")
            raise

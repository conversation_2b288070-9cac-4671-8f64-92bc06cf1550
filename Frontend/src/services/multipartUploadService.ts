import { API_CONFIG, FILE_CONFIG } from "@/constants";
import type {
  MultipartUploadInitiateRequest,
  MultipartUploadInitiateResponse,
  MultipartUploadPartRequest,
  MultipartUploadPartResponse,
  MultipartUploadCompleteRequest,
  MultipartUploadCompleteResponse,
  MultipartUploadAbortRequest,
  MultipartUploadAbortResponse,
  MultipartUploadPart,
} from "@/types/api";
import api from "./api";

export interface UploadProgress {
  uploadedBytes: number;
  totalBytes: number;
  percentage: number;
  currentPart: number;
  totalParts: number;
}

export interface MultipartUploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  onPartComplete?: (partNumber: number, etag: string) => void;
  onError?: (error: Error) => void;
  chunkSize?: number;
  maxConcurrentParts?: number;
}

class MultipartUploadService {
  private async initiateUpload(
    filename: string,
    filesize: number,
    chunkSize?: number
  ): Promise<MultipartUploadInitiateResponse> {
    const request: MultipartUploadInitiateRequest = {
      filename,
      filesize,
      chunk_size: chunkSize,
    };

    const response = await api.post<MultipartUploadInitiateResponse>(
      API_CONFIG.ENDPOINTS.MULTIPART_INITIATE,
      request
    );
    return response.data;
  }

  private async getPartUploadUrl(
    key: string,
    uploadId: string,
    partNumber: number
  ): Promise<MultipartUploadPartResponse> {
    const request: MultipartUploadPartRequest = {
      key,
      upload_id: uploadId,
      part_number: partNumber,
    };

    const response = await api.post<MultipartUploadPartResponse>(
      API_CONFIG.ENDPOINTS.MULTIPART_PART_URL,
      request
    );
    return response.data;
  }

  private async uploadPart(
    uploadUrl: string,
    chunk: Blob,
    partNumber: number
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.open("PUT", uploadUrl);
      xhr.setRequestHeader("Content-Type", "application/octet-stream");

      xhr.onload = () => {
        if (xhr.status === 200) {
          const etag = xhr.getResponseHeader("ETag");
          if (etag) {
            resolve(etag.replace(/"/g, ""));
          } else {
            reject(new Error(`No ETag received for part ${partNumber}`));
          }
        } else {
          reject(new Error(`Upload failed for part ${partNumber}: ${xhr.status}`));
        }
      };

      xhr.onerror = () => reject(new Error(`Network error uploading part ${partNumber}`));
      xhr.send(chunk);
    });
  }

  private async completeUpload(
    key: string,
    uploadId: string,
    parts: MultipartUploadPart[]
  ): Promise<MultipartUploadCompleteResponse> {
    const request: MultipartUploadCompleteRequest = {
      key,
      upload_id: uploadId,
      parts: parts.sort((a, b) => a.part_number - b.part_number),
    };

    const response = await api.post<MultipartUploadCompleteResponse>(
      API_CONFIG.ENDPOINTS.MULTIPART_COMPLETE,
      request
    );
    return response.data;
  }

  private async abortUpload(key: string, uploadId: string): Promise<void> {
    const request: MultipartUploadAbortRequest = {
      key,
      upload_id: uploadId,
    };

    await api.post<MultipartUploadAbortResponse>(
      API_CONFIG.ENDPOINTS.MULTIPART_ABORT,
      request
    );
  }

  private createChunks(file: File, chunkSize: number): Blob[] {
    const chunks: Blob[] = [];
    let start = 0;

    while (start < file.size) {
      const end = Math.min(start + chunkSize, file.size);
      chunks.push(file.slice(start, end));
      start = end;
    }

    return chunks;
  }

  private async uploadPartsSequentially(
    chunks: Blob[],
    key: string,
    uploadId: string,
    options: MultipartUploadOptions
  ): Promise<MultipartUploadPart[]> {
    const parts: MultipartUploadPart[] = [];
    let uploadedBytes = 0;

    for (let i = 0; i < chunks.length; i++) {
      const partNumber = i + 1;
      const chunk = chunks[i];

      try {
        const partResponse = await this.getPartUploadUrl(key, uploadId, partNumber);
        const etag = await this.uploadPart(partResponse.upload_url, chunk, partNumber);

        parts.push({ part_number: partNumber, etag });
        uploadedBytes += chunk.size;

        options.onPartComplete?.(partNumber, etag);
        options.onProgress?.({
          uploadedBytes,
          totalBytes: chunks.reduce((sum, c) => sum + c.size, 0),
          percentage: Math.round((uploadedBytes / chunks.reduce((sum, c) => sum + c.size, 0)) * 100),
          currentPart: partNumber,
          totalParts: chunks.length,
        });
      } catch (error) {
        throw new Error(`Failed to upload part ${partNumber}: ${error}`);
      }
    }

    return parts;
  }

  async uploadFile(file: File, options: MultipartUploadOptions = {}): Promise<void> {
    const chunkSize = options.chunkSize || FILE_CONFIG.MULTIPART_CHUNK_SIZE;
    let uploadId: string | null = null;
    let key: string | null = null;

    try {
      const initResponse = await this.initiateUpload(file.name, file.size, chunkSize);
      uploadId = initResponse.upload_id;
      key = initResponse.key;

      const chunks = this.createChunks(file, initResponse.chunk_size);
      const parts = await this.uploadPartsSequentially(chunks, key, uploadId, options);

      await this.completeUpload(key, uploadId, parts);
    } catch (error) {
      if (uploadId && key) {
        try {
          await this.abortUpload(key, uploadId);
        } catch (abortError) {
          console.error("Failed to abort upload:", abortError);
        }
      }
      
      const errorMessage = error instanceof Error ? error.message : "Upload failed";
      options.onError?.(new Error(errorMessage));
      throw error;
    }
  }

  shouldUseMultipart(fileSize: number): boolean {
    return fileSize > FILE_CONFIG.MULTIPART_THRESHOLD;
  }
}

export const multipartUploadService = new MultipartUploadService();

export interface PresignedUrlRequest {
  filename: string;
  filesize: number;
}

export interface PresignedUrlResponse {
  url: string;
  key: string;
  expires_in: number;
}

export interface FileInfo {
  key: string;
  filename: string;
  size: number;
  last_modified: string;
  etag?: string;
}

export interface FileListResponse {
  files: FileInfo[];
  total_count: number;
  total_size: number;
}

export interface FileDeleteResponse {
  success: boolean;
  message: string;
  deleted_key: string;
}

export interface ApiError {
  detail: string;
  status?: number;
}

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
}

// Multipart Upload Types
export interface MultipartUploadInitiateRequest {
  filename: string;
  filesize: number;
  chunk_size?: number;
}

export interface MultipartUploadInitiateResponse {
  upload_id: string;
  key: string;
  chunk_size: number;
  total_parts: number;
  expires_in: number;
}

export interface MultipartUploadPartRequest {
  key: string;
  upload_id: string;
  part_number: number;
}

export interface MultipartUploadPartResponse {
  upload_url: string;
  part_number: number;
  expires_in: number;
}

export interface MultipartUploadPart {
  part_number: number;
  etag: string;
}

export interface MultipartUploadCompleteRequest {
  key: string;
  upload_id: string;
  parts: MultipartUploadPart[];
}

export interface MultipartUploadCompleteResponse {
  success: boolean;
  key: string;
  etag: string;
  location: string;
}

export interface MultipartUploadAbortRequest {
  key: string;
  upload_id: string;
}

export interface MultipartUploadAbortResponse {
  success: boolean;
  message: string;
}
